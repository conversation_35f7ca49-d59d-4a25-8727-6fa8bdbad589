using System;
using System.Windows.Forms;

namespace TrayApp
{
    static class Program
    {
        static NotifyIcon trayIcon;
        static ContextMenuStrip trayMenu;

        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            trayMenu = new ContextMenuStrip();
            trayMenu.Items.Add("Show Console", null, ShowConsole);
            trayMenu.Items.Add("Hide Console", null, HideConsole);
            trayMenu.Items.Add("On", null, OnAction);
            trayMenu.Items.Add("Off", null, OffAction);
            trayMenu.Items.Add("Exit", null, ExitApp);

            trayIcon = new NotifyIcon
            {
                Text = "Tray Tool",
                Icon = SystemIcons.Application,
                ContextMenuStrip = trayMenu,
                Visible = true
            };

            Application.Run();
        }

        static void ShowConsole(object sender, EventArgs e)
        {
            Console.WriteLine("Console shown.");
            ShowWindow(GetConsoleWindow(), 5); // SW_SHOW
        }

        static void HideConsole(object sender, EventArgs e)
        {
            Console.WriteLine("Console hidden.");
            ShowWindow(GetConsoleWindow(), 0); // SW_HIDE
        }

        static void OnAction(object sender, EventArgs e)
        {
            Console.WriteLine("Turned ON");
        }

        static void OffAction(object sender, EventArgs e)
        {
            Console.WriteLine("Turned OFF");
        }

        static void ExitApp(object sender, EventArgs e)
        {
            trayIcon.Visible = false;
            Application.Exit();
        }

        [System.Runtime.InteropServices.DllImport("kernel32.dll")]
        private static extern IntPtr GetConsoleWindow();

        [System.Runtime.InteropServices.DllImport("user32.dll")]
        private static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);
    }
}
